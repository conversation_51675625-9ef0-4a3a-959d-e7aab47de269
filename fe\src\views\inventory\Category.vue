<template>
  <div class="category-root">
    <div class="category-header">
      <div class="category-header-title">
        <span>类别设置</span>
      </div>
      <div class="category-header-action">
        <el-button type="primary" :icon="Plus" @click="handleAddCategory">
          新增类别
        </el-button>
      </div>
    </div>
    <div class="category-container">
      <div class="category-content">
        <el-table
          :data="mockData"
          style="width: 100%"
          row-key="id"
          border
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :header-cell-style="{
            background: 'rgb(248,249,252)',
            color: '#030814',
            fontSize: '16px',
          }"
        >
          <el-table-column prop="name" label="名称" />
          <el-table-column prop="code" label="编码" />
          <el-table-column prop="remark" label="备注" />
          <el-table-column prop="operations" label="操作">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleAddSubcategory(row)">
                添加分类
              </el-button>
              <el-button link type="primary"> 编辑 </el-button>
              <el-button link type="primary"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { SearchContainer, CommonTable } from "@/base-components";
import { reactive, ref } from "vue";
import { Plus, ArrowDown, ArrowRight } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

// 搜索表单
const searchForm = reactive({
  name: "",
  code: "",
});

const requestParams = reactive({
  filters: "",
});

// 表格列配置
const categoryColumns = [
  {
    prop: "name",
    label: "名称",
    minWidth: "300px",
  },
  {
    prop: "code",
    label: "编码",
    minWidth: "150px",
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: "300px",
  },
  {
    prop: "operations",
    label: "操作",
    minWidth: "200px",
    fixed: "right",
  },
];

// 模拟数据 - 实际项目中应该从API获取
const mockData = [
  {
    id: 1,
    name: "电子设备及原器件",
    code: "1",
    remark: "",
    expanded: true,
    children: [
      {
        id: 11,
        name: "计算机及网络设备",
        code: "11",
        remark: "包含计算机以及网络设备等配件",
        children: [],
      },
      {
        id: 12,
        name: "计算机组件",
        code: "12",
        remark: "--",
        children: [],
      },
      {
        id: 13,
        name: "计算机外部设备",
        code: "13",
        remark: "--",
        children: [],
      },
    ],
  },
  {
    id: 2,
    name: "原材料",
    code: "2",
    remark: "",
    expanded: false,
    children: [],
  },
  {
    id: 3,
    name: "燃料",
    code: "3",
    remark: "",
    expanded: false,
    children: [],
  },
  {
    id: 4,
    name: "机械设备及配件",
    code: "4",
    remark: "",
    expanded: false,
    children: [],
  },
  {
    id: 5,
    name: "电器设备及配件",
    code: "5",
    remark: "",
    expanded: false,
    children: [],
  },
  {
    id: 6,
    name: "仪器仪表及计量器具",
    code: "6",
    remark: "",
    expanded: false,
    children: [],
  },
];

// 模拟API调用
const getCategoryList = async (params) => {
  // 模拟异步请求
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          data: {
            rows: mockData,
            pageElements: {
              totalElements: mockData.length,
            },
          },
        },
      });
    }, 300);
  });
};

function dataCallback(res) {
  const data = res.data?.data?.rows || [];
  return {
    tableRows: data,
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

// 新增类别
const handleAddCategory = () => {
  ElMessage.info("新增类别功能待实现");
};

// 添加子分类
const handleAddSubcategory = (row) => {
  ElMessage.info(`为 ${row.name} 添加子分类功能待实现`);
};

// 更多操作
const handleMore = (row) => {
  ElMessage.info(`${row.name} 的更多操作功能待实现`);
};
</script>

<style lang="less" scoped>
.category-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
  }

  .category-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .category-content {
      margin-top: 10px;
      flex: 1;
      min-height: 0px;
    }
  }

  .category-name {
    display: flex;
    align-items: center;
    gap: 8px;

    .expand-icon {
      font-size: 12px;
      color: #909399;
      cursor: pointer;
    }
  }
}
</style>
